{include file="header_amministratore.tpl"}

<br>
<table width='100%' class="sfondo_base_generico contenitore_generico bordo_generico_rilievo" style="border-collapse: separate;">
<tr>
	<td valign='top' width='100%'>
	<table width='100%'>
		<tr>
			<td align='center' colspan='7'>
				<font color='#000000' size='5'>Manutenzione: selezionare l'opzione desiderata </font>
			</td>
		</tr>
		{if $scuola_bloccata == 'SI' }
		<tr>
			<td align='center' colspan='7'>
				<font color='#ff0000' size='5'><blink>Attenzione: scuola bloccata!!</blink></font>
			</td>
		</tr>
		{/if}
	</table>
	</td>
</tr>

{* {{{ Nuovi menu manutenzione *}
<tr>
	<td valign='top' width='100%' align='center'>
	<form method='post' action='{$SCRIPT_NAME}'>
		<table>
			<tr>
				<td align='center'>
					<a name='elenco funzioni'></a>
					<b><font color='#000000' size='2'>A - Log</font></b>
				</td>
				<td align='center'>
					<b><font color='#000000' size='2'>B - Varie</font></b>
				</td>
		</tr>
			<tr>
				<td align='center'>
					{mastercom_auto_select name="manutenzione" size=5 ondblclick="this.form.tipo_manutenzione.value=this.value; this.form.submit();" menu_prefix='A' style="width:100%"}
						log_docenti_display###Log Docenti@@@
                        log_ins_mod_del###Chi ha inserito/modificato/eliminato dati@@@
                        ips_ids###IPS/IDS (Rilevamento situazioni sospette)@@@
                        log_mensa_display###Log Mensa Studenti@@@
                        log_parenti_display###Log Parenti
					{/mastercom_auto_select}
				</td>
				<td align='center'>
					{mastercom_auto_select name="manutenzione" size=4 ondblclick="this.form.tipo_manutenzione.value=this.value; this.form.submit();" menu_prefix='B' style="width:100%"}
						compiti_scuola###Compiti della scuola
					{/mastercom_auto_select}
				</td>
			</tr>
		</table>

		<input type='hidden' name='form_stato' value='{$form_stato}'>
		<input type='hidden' name='stato_principale' value='{$stato_principale}'>
		<input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
		<input type='hidden' name='tipo_manutenzione' value=''>
		<input type='hidden' name='current_user' value='{$current_user}'>
		<input type='hidden' name='current_key' value='{$current_key}'>
	</form>

	</td>
</tr>
{* }}} *}


{* {{{ Log registro docenti *}

{if $tipo_manutenzione == 'log_docenti_display' or $tipo_manutenzione == 'log_docenti_update'}

<form method='post' action='{$SCRIPT_NAME}'>
<tr>
	<td valign='top' width='100%'>
	<table width='100%' align='center'>
        <tr>
            <td ALIGN='CENTER' COLSPAN='6'><br><font color='#000000' size='4'>LOG DOCENTI</font></td>
            <td>
         <tr>
            <td><font color='#000000' size='2'>Data:</font>
				<input type='text' name='gg' size='2' value='{$d}'>
                <input type='text' name='mm' size='2' value='{$m}'>
                <input type='text' name='aaaa' size='4' value='{$y}'>
            </td>
            <td>
				<font color='#000000' size='2'>Utente:</font>
				{mastercom_smart_select
					nome_pulsante="visualizzazione_abbinamenti"
					contesto="istituto"
					nome_select="id_professore"
					classe="classeselect_base"
					utente_corrente=$current_user
				}
					<OPTION value="TUTTI_PROF">Tutti i professori</OPTION>
					{section name=cont1 loop=$array_prof}
					<OPTION value="{$array_prof[cont1].valore}">{$array_prof[cont1].cognome} {$array_prof[cont1].nome}</OPTION>
											{/section}
										{/mastercom_smart_select}
			</td>
            <td><input type='checkbox' name='errori'><font color='#000000' size='2'> Solo errori</font></td>
        </tr>
		<tr>
            <td colspan ="6"><input type='checkbox' name='accessi_falliti'><font color='#000000' size='2'> Solo accessi falliti</font></td>
		</tr>
        <tr>
            <td ALIGN='CENTER' COLSPAN='6'>
				<input type='image' name='bottone' value='Stampa' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
				<input type='hidden' name='form_stato' value='{$form_stato}'>
				<input type='hidden' name='stato_principale' value='{$stato_principale}'>
				<input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
				<input type='hidden' name='tipo_manutenzione' value='log_docenti_update'>
				<input type='hidden' name='current_user' value='{$current_user}'>
				<input type='hidden' name='current_key' value='{$current_key}'>
			</td>
        </tr>
    </table>
</form>
	</td>
</tr>
{/if}
{if $tipo_manutenzione == 'log_docenti_update'}
<tr>
	<td valign='top' width='100%'>
	{if $stato_log == 'visualizza_accessi_falliti' }
	<table width='100%' align='center'>
        <tr>
            <td ALIGN='CENTER' COLSPAN='4'><br><font color='#000000' size='4'>LOG DOCENTI CON ACCESSI FALLITI</font></td>
		</tr>
	    <tr>
		   <td ALIGN='CENTER'><br><b><font color='#000000' size='2'>DATA</font></b></td>
		   <td ALIGN='CENTER'><br><b><font size='2'>USER</font></b></td>
		   <td ALIGN='CENTER'><br><b><font size='2'>PASSWORD SBAGLIATA</font></b></td>
		   <td ALIGN='CENTER'><br><b><font size='2'>TIPO AUTENTICAZIONE CON USB</font></b></td>
		</tr>
		{section name=cont1 loop=$log_docenti}
		   <tr>
			   <td ALIGN='CENTER'><br><font color='#000000' size='2'>{$log_docenti[cont1].data_finale}</font></td>
			   <td ALIGN='CENTER'><br><font size='2'>{$log_docenti[cont1].dati_extra.0}</font></td>
			   <td ALIGN='CENTER'><br><font size='2'>{$log_docenti[cont1].dati_extra.1}</font></td>
			   <td ALIGN='CENTER'><br><font size='2'>{$log_docenti[cont1].dati_extra.2}</font></td>
		  </tr>
		{/section}
		</tr>
     </table>
	{elseif $stato_log == 'visualizza_singolo_docente' }
	<table width='100%' align='center'>
        <tr>
            <td ALIGN='CENTER' COLSPAN='2'><br><font color='#000000' size='4'>LOG REGISTRO DEL DOCENTE:{$log_docenti[0].cognome} {$log_docenti[0].nome}</font></td>
		</tr>
			{section name=cont1 loop=$log_docenti}
		   <tr>
		   	{if $log_docenti[cont1].cosa == 'LOGIN REGISTRO'}
			<tr>
			   <td ALIGN='CENTER' colspan='3'><br><B><font color='#aa00aa' size='3'>NUOVA SESSIONE</font></B></td>
			</tr>
			{/if}
			<tr>
			   <td ALIGN='CENTER'><br><b><font color='#000000' size='2'>DATA</font></b></td>
			   <td ALIGN='CENTER'><br><b><font size='2'>DESCRIZIONE EVENTO</font></b></td>
			</tr>
			   <td ALIGN='CENTER'><br><font color='#000000' size='2'>{$log_docenti[cont1].data_finale}</font></td>
			   <td ALIGN='LEFT'><br><font size='2'>{$log_docenti[cont1].cosa}</font></td>
			</tr>
		{/section}
		</tr>
     </table>
	{elseif $stato_log == 'visualizza_tutto' }
	<table width='100%' align='center'>
        <tr>
            <td ALIGN='CENTER' COLSPAN='3'><br><font color='#000000' size='4'>LOG DOCENTI ESTRATTO</font></td>
		</tr>
		<tr>
		   <td ALIGN='CENTER'><br><b><font color='#000000' size='2'>DATA</font></b></td>
		   <td ALIGN='CENTER'><br><b><font size='2'>USER</font></b></td>
		   <td ALIGN='CENTER'><br><b><font size='2'>DESCRIZIONE EVENTO</font></b></td>
		</tr>

		{section name=cont1 loop=$log_docenti}
		    <tr>
			   <td ALIGN='CENTER'><br><font color='#000000' size='2'>{$log_docenti[cont1].data_finale}</font></td>
			   <td ALIGN='CENTER'><br><font color='#000000' size='2'>{$log_docenti[cont1].cognome} {$log_docenti[cont1].nome}</font></td>
			   <td ALIGN='LEFT'><br><font size='2'>{$log_docenti[cont1].cosa}</font></td>
			</tr>
		{/section}
		</tr>
     </table>
	{/if}
	</td>
</tr>
{/if}

{* }}} *}


{* {{{ Log mensa studenti *}

{if $tipo_manutenzione == 'log_mensa_display' or $tipo_manutenzione == 'log_mensa_update'}
    <form method='post' action='{$SCRIPT_NAME}'>
    <tr>
        <td valign='top' width='100%'>
            <table width='100%' align='center'>
                <tr>
                    <td ALIGN='CENTER' COLSPAN='2'><br><font color='#000000' size='4'>LOG MENSA STUDENTI</font></td>
                    <td>
                 <tr>
                    <td><font color='#000000' size='2'>Data su cui fare la verifica:</font>
                        <input type='text' name='gg' size='2' value='{$d}'>
                        <input type='text' name='mm' size='2' value='{$m}'>
                        <input type='text' name='aaaa' size='4' value='{$y}'>
                    </td>
                    <td>
                        <font color='#000000' size='2'>Utente:</font>
                        {mastercom_smart_select
                                nome_pulsante="visualizzazione_abbinamenti"
                                contesto="istituto"
                                nome_select="id_studente"
                                classe="classeselect_base"
                                utente_corrente=$current_user
                        }
                        {foreach from=$elenco_studenti_mensa item=studente}
                            <OPTION value="{$studente.id}">{$studente.cognome} {$studente.nome}</OPTION>
                        {/foreach}
                        {/mastercom_smart_select}
                    </td>
                </tr>
                <tr>
                    <td ALIGN='CENTER' COLSPAN='2'>
                        <input type='image' name='bottone' value='Stampa' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                        <input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
                        <input type='hidden' name='tipo_manutenzione' value='log_mensa_update'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    </form>
{/if}

{if $tipo_manutenzione == 'log_mensa_update'}
    <tr>
        <td valign='top' width='100%'>
            <style>
                /* Stile CSS inline per la tabella */
                .styled-table {
                  border-collapse: collapse;
                  width: 100%;
                  margin: 0 auto; /* Centra la tabella nella larghezza disponibile */
                }

                .styled-table th, .styled-table td {
                  padding: 8px;
                  border-left: 1px solid #ccc; /* Aggiunge una linea divisoria a sinistra di ciascuna cella */
                  border-top: 1px solid #ccc; /* Aggiunge una linea divisoria in alto di ciascuna cella */
                  border-bottom: 1px solid #ccc; /* Aggiunge una linea divisoria in basso di ciascuna cella */
                }

                .styled-table th {
                  background-color: #f2f2f2; /* Sfondo grigio per le celle dell'intestazione */
                }

                .styled-table td {
                  background-color: #e6f7ff; /* Colore di sfondo più gradevole per le celle normali */
                }
            </style>
            <table class="styled-table" style="margin-left: 0;" align='center'>
                <tr>
                    <th ALIGN='CENTER' COLSPAN='4'><br><font color='#000000' size='4'>LOG MENSA DETTAGLIO PASTI DI {$dati_studente.cognome} {$dati_studente.nome}</font></th>
                </tr>
                <tr>
                       <th ALIGN='CENTER'><br><b><font color='#000000' size='2'>DATA PASTO</font></b></th>
                       <th ALIGN='CENTER'><br><b><font size='2'>DESCRIZIONE OPERAZIONE</font></b></th>
                </tr>
                {foreach from=$log_mensa_dettaglio item=singolo_log}
                    <tr>
                        <td ALIGN='CENTER'><br><font color='#000000' size='2'>{$singolo_log.data}</font></td>
                        <td ALIGN='LEFT'><br><font size='2'>{$singolo_log.descrizione}</font></td>
                    </tr>
                {/foreach}
            </table>
        </td>
    </tr>
{/if}

{* }}} *}

{* {{{ Log Parenti *}

{if $tipo_manutenzione == 'log_parenti_display' or $tipo_manutenzione == 'log_parenti_update'}
    <form method='post' action='{$SCRIPT_NAME}'>
    <tr>
        <td valign='top' width='100%'>
            <table width='100%' align='center'>
                <tr>
                    <td ALIGN='CENTER' COLSPAN='2'><br><font color='#000000' size='4'>LOG PARENTI</font></td>
                    <td>
                 <tr>
                    <td><font color='#000000' size='2'>Campo da cercare (es. codice fiscale, nome, cognome):</font>
                        <input type='text' name='campo_cercato' size='30' value='{$campo_cercato_parenti}' placeholder='Inserisci dato da cercare'>
                    </td>
                </tr>
                <tr>
                    <td ALIGN='CENTER' COLSPAN='2'>
                        <input type='image' name='bottone' value='Cerca' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                        <input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
                        <input type='hidden' name='tipo_manutenzione' value='log_parenti_update'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    </form>

    {* Visualizzazione risultati LOG PARENTI *}
    {if $log_parenti_risultati}
        {if $log_parenti_risultati|@count > 0}
            <tr>
                <td valign='top' width='100%'>
                    <style>
                        .styled-table {
                          border-collapse: collapse;
                          width: 100%;
                          max-width: 900px;
                          margin: 0 auto;
                          border: 2px solid #ccc;
                        }

                        .styled-table th, .styled-table td {
                          padding: 8px;
                          border-bottom: 1px solid #ddd;
                        }

                        .styled-table th {
                          background-color: #f2f2f2;
                        }

                        .styled-table td {
                          background-color: #e6f7ff;
                        }
                    </style>

                    <table class="styled-table" style="margin-left: 0;" align='center'>
                        <tr>
                            <th ALIGN='CENTER' COLSPAN='2'><br><font color='#000000' size='4'>RISULTATI RICERCA LOG PARENTI - CAMPO: {$campo_cercato_parenti}</font></th>
                        </tr>
                    </table><br>

                    {foreach from=$log_parenti_risultati key=db_nome item=logs_db}
                        {foreach from=$logs_db key=id_log item=singolo_log}
                            <table class="styled-table" style="margin-left: 0;" align='center'>
                                <tr>
                                    <th ALIGN='CENTER' COLSPAN='2'><br><font color='#000000' size='3'>MODIFICHE EFFETTUATE NEL DB {$db_nome} IL: {$singolo_log.quando} da {$singolo_log.quale_utente}</font></th>
                                </tr>
                                {foreach from=$singolo_log.modifiche item=modifica}
                                    <tr>
                                        <td ALIGN='LEFT' COLSPAN='2'><font size='2'>{$modifica}</font></td>
                                    </tr>
                                {/foreach}
                            </table><br><br>
                        {/foreach}
                    {/foreach}
                </td>
            </tr>
        {else}
            <tr>
                <td valign='top' width='100%'>
                    <table class="styled-table" style="margin-left: 0;" align='center'>
                        <tr>
                            <th ALIGN='CENTER'><br><font color='#000000' size='4'>NESSUN LOG TROVATO PER LA CHIAVE INDICATA: {$campo_cercato_parenti}</font></th>
                        </tr>
                    </table>
                </td>
            </tr>
        {/if}
    {/if}
{/if}

{* }}} *}

{* {{{ Compiti della scuola *}

{if $tipo_manutenzione == 'compiti_scuola'}

	<tr>
		<td valign='top' width='100%'>
		<table width='100%' align='center'>
			<tr>
				<td ALIGN='CENTER' COLSPAN='4'><br><font color='#000000' size='4'>COMPITI ASSEGNATI</font></td>
				<td>
			 <tr>
				<td><font color='#000000' size='2' COLSPAN='4'>Selezionare la visualizzazione desiderata</font></td>
				<form method='post' action='{$SCRIPT_NAME}' id="form_compiti_aperti">
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
						<input type='hidden' name='tipo_manutenzione' value='compiti_scuola'>
						<input type='hidden' name='stato_compiti' value='compiti_aperti'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
				</form>
				<form method='post' action='{$SCRIPT_NAME}'>
					<td ALIGN='CENTER'>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
						<input type='hidden' name='tipo_manutenzione' value='compiti_scuola'>
						<input type='hidden' name='stato_compiti' value='compiti_totali'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</td>
				</form>
				<form method='post' action='{$SCRIPT_NAME}'>
					<td ALIGN='CENTER'>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
						<input type='hidden' name='tipo_manutenzione' value='compiti_scuola'>
						<input type='hidden' name='stato_compiti' value='sviluppi_aperti'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</td>
				</form>
				<form method='post' action='{$SCRIPT_NAME}'>
					<td ALIGN='CENTER'>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
						<input type='hidden' name='tipo_manutenzione' value='compiti_scuola'>
						<input type='hidden' name='stato_compiti' value='sviluppi_totali'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</td>
				</form>
			</tr>
		</table>


	<div id="tab_compiti">
		<ul>
			<li><a id="btn_aperti" href="#compiti_aperti">Segnalazioni aperte</a></li>
			<li><a id="btn_totali" href="#compiti_totali">Segnalazioni totali</a></li>
			<li><a id="btn_sviluppi_aperti" href="#sviluppi_aperti">Nuovi Sviluppi aperti</a></li>
			<li><a id="btn_sviluppi_totali" href="#sviluppi_totali">Nuovi Sviluppi totali</a></li>
		</ul>

		{literal}
		<style>
		.compito_title {
			font-weight: bold;
			font-size: 13px;
			height: 20px;
			margin-top: 5px;
			cursor: pointer;
		}
		DIV.compito {
			padding: 3px;
			border-bottom: 1px solid #CCC;
		}
		.stato_compito {
			float: left;
			width: 25px;
			height: 17px;
			margin: 3px;
			border-radius: 5px;
			-moz-border-radius: 5px;
			-webkit-border-radius: 5px;
		}
		.stato_compito_basso {
			background-color : #22BB22;
		}
		.stato_compito_medio {
			background-color : #FFFF00;
			border: 1px solid #CCAA00;
		}
		.stato_compito_alto {
			background-color : #FF0000;
		}
		.compito_desc {
			font-size: 13px;
			margin-top: 5px;
			padding: 5px;
			padding-left: 30px;
		}
		</style>
		{/literal}

		<div id="compiti_aperti">
			{foreach from=$elenco_compiti item=compito_attuale}
				{if $compito_attuale.data_chiusura == 0 and $compito_attuale.tipo == 'error'}
					<div class="compito">
						{if $compito_attuale.emergenza_tradotta == 'BASSO'}
							<div class="stato_compito stato_compito_basso"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'MEDIO'}
							<div class="stato_compito stato_compito_medio"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'ALTO'}
							<div class="stato_compito stato_compito_alto"></div>
						{/if}
						{if $compito_attuale.compito_letto == 'NO'}
							<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" >
							Ultimo aggiornamento del: {$compito_attuale.data_aggiornamento_tradotta} - Aperto il : {$compito_attuale.data_apertura_tradotta} - {$compito_attuale.titolo}
						{else}
							<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" style='font-weight: normal;'>
							Ultimo aggiornamento del: {$compito_attuale.data_aggiornamento_tradotta} - Aperto il : {$compito_attuale.data_apertura_tradotta} - {$compito_attuale.titolo}
						{/if}
						</div>
						<div class="compito_desc ui-widget-content ui-corner-all">
						Data ultimo aggiornamento: {$compito_attuale.data_aggiornamento_tradotta}<br>
						Livello urgenza: {$compito_attuale.emergenza_tradotta}<br>
						Status: {$compito_attuale.status}<br>
						Descrizione:
							<div class="compito_desc ui-widget-content ui-corner-all">
								{$compito_attuale.descrizione}
							</div>
						Risposta MasterTraining -
						{if $compito_attuale.titolo_risposta == ''}
							Settore Assistenza:
						{else}
							{$compito_attuale.titolo_risposta}:
						{/if}
							<div class="compito_desc ui-widget-content ui-corner-all">
								{if $compito_attuale.risposta == ''}
									In attesa di risoluzione....
								{else}
									{$compito_attuale.risposta}
								{/if}
							</div>

						</div>
					</div>
				{/if}
			{/foreach}
		</div>

		<div id="compiti_totali">
			{foreach from=$elenco_compiti item=compito_attuale}
				{if $compito_attuale.tipo == 'error'}
					<div class="compito">
						{if $compito_attuale.data_chiusura > 0}
							<div class="stato_compito stato_compito_chiuso"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'BASSO'}
							<div class="stato_compito stato_compito_basso"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'MEDIO'}
							<div class="stato_compito stato_compito_medio"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'ALTO'}
							<div class="stato_compito stato_compito_alto"></div>
						{/if}
						{if $compito_attuale.data_chiusura > 0}
							{if $compito_attuale.compito_letto == 'NO'}
								<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" >
								Chiuso il : {$compito_attuale.data_chiusura_tradotta} - {$compito_attuale.titolo}
							{else}
								<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" style='font-weight: normal;'>
								Chiuso il : {$compito_attuale.data_chiusura_tradotta} - {$compito_attuale.titolo}
							{/if}
						{else}
							{if $compito_attuale.compito_letto == 'NO'}
								<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" >
								Ultimo aggiornamento del: {$compito_attuale.data_aggiornamento_tradotta} - Aperto il : {$compito_attuale.data_apertura_tradotta} - {$compito_attuale.titolo}
							{else}
								<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" style='font-weight: normal;'>
								Ultimo aggiornamento del: {$compito_attuale.data_aggiornamento_tradotta} - Aperto il : {$compito_attuale.data_apertura_tradotta} - {$compito_attuale.titolo}
							{/if}
						{/if}
						</div>
						<div class="compito_desc ui-widget-content ui-corner-all">
                        {if $compito_attuale.data_chiusura > 0}
                            Data apertura segnalazione: {$compito_attuale.data_apertura_tradotta}<br>
                        {else}
                            Data ultimo aggiornamento: {$compito_attuale.data_aggiornamento_tradotta}<br>
                        {/if}
						Livello urgenza: {$compito_attuale.emergenza_tradotta}<br>
						Status: {$compito_attuale.status}<br>
						Descrizione:
							<div class="compito_desc ui-widget-content ui-corner-all">
								{$compito_attuale.descrizione}
							</div>
						Risposta MasterTraining -
						{if $compito_attuale.titolo_risposta == ''}
							Settore Assistenza:
						{else}
							{$compito_attuale.titolo_risposta}:
						{/if}
							<div class="compito_desc ui-widget-content ui-corner-all">
								{if $compito_attuale.risposta == ''}
									In attesa di risoluzione....
								{else}
									{$compito_attuale.risposta}
								{/if}
							</div>

						</div>
					</div>
				{/if}
			{/foreach}

		</div>

		<div id="sviluppi_aperti">
			{foreach from=$elenco_compiti item=compito_attuale}
				{if $compito_attuale.data_chiusura == 0 and $compito_attuale.tipo == 'task'}
					<div class="compito">
						{if $compito_attuale.emergenza_tradotta == 'BASSO'}
							<div class="stato_compito stato_compito_basso"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'MEDIO'}
							<div class="stato_compito stato_compito_medio"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'ALTO'}
							<div class="stato_compito stato_compito_alto"></div>
						{/if}
						{if $compito_attuale.compito_letto == 'NO'}
							<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" >
							Ultimo aggiornamento del: {$compito_attuale.data_aggiornamento_tradotta} - Aperto il : {$compito_attuale.data_apertura_tradotta} - {$compito_attuale.titolo}
						{else}
							<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" style='font-weight: normal;'>
							Ultimo aggiornamento del: {$compito_attuale.data_aggiornamento_tradotta} - Aperto il : {$compito_attuale.data_apertura_tradotta} - {$compito_attuale.titolo}
						{/if}
						</div>
						<div class="compito_desc ui-widget-content ui-corner-all">
						Data ultimo aggiornamento: {$compito_attuale.data_aggiornamento_tradotta}<br>
						Livello urgenza: {$compito_attuale.emergenza_tradotta}<br>
						Status: {$compito_attuale.status}<br>
						Descrizione:
							<div class="compito_desc ui-widget-content ui-corner-all">
								{$compito_attuale.descrizione}
							</div>
						Risposta MasterTraining -
						{if $compito_attuale.titolo_risposta == ''}
							Settore Sviluppo:
						{else}
							{$compito_attuale.titolo_risposta}:
						{/if}
							<div class="compito_desc ui-widget-content ui-corner-all">
								{if $compito_attuale.risposta == ''}
									In attesa di valutazione....
								{else}
									{$compito_attuale.risposta}
								{/if}
							</div>
						</div>
					</div>
				{/if}
			{/foreach}
		</div>

		<div id="sviluppi_totali">
			{foreach from=$elenco_compiti item=compito_attuale}
				{if $compito_attuale.tipo == 'task'}
					<div class="compito">
						{if $compito_attuale.data_chiusura > 0}
							<div class="stato_compito stato_compito_chiuso"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'BASSO'}
							<div class="stato_compito stato_compito_basso"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'MEDIO'}
							<div class="stato_compito stato_compito_medio"></div>
						{elseif $compito_attuale.emergenza_tradotta == 'ALTO'}
							<div class="stato_compito stato_compito_alto"></div>
						{/if}
						{if $compito_attuale.data_chiusura > 0}
							{if $compito_attuale.compito_letto == 'NO'}
								<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" >
								Chiuso il : {$compito_attuale.data_chiusura_tradotta} - {$compito_attuale.titolo}
							{else}
								<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" style='font-weight: normal;'>
								Chiuso il : {$compito_attuale.data_chiusura_tradotta} - {$compito_attuale.titolo}
							{/if}
						{else}
							{if $compito_attuale.compito_letto == 'NO'}
								<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" >
								Ultimo aggiornamento del: {$compito_attuale.data_aggiornamento_tradotta} - Aperto il : {$compito_attuale.data_apertura_tradotta} - {$compito_attuale.titolo}
							{else}
								<div id="compito_{$compito_attuale.id_notizia}" class="compito_title" style='font-weight: normal;'>
								Ultimo aggiornamento del: {$compito_attuale.data_aggiornamento_tradotta} - Aperto il : {$compito_attuale.data_apertura_tradotta} - {$compito_attuale.titolo}
							{/if}
						{/if}
						</div>
						<div class="compito_desc ui-widget-content ui-corner-all">
						Data ultimo aggiornamento: {$compito_attuale.data_aggiornamento_tradotta}<br>
						Livello urgenza: {$compito_attuale.emergenza_tradotta}<br>
						Status: {$compito_attuale.status}<br>
						Descrizione:
							<div class="compito_desc ui-widget-content ui-corner-all">
								{$compito_attuale.descrizione}
							</div>
						Risposta MasterTraining -
						{if $compito_attuale.titolo_risposta == ''}
							Settore Sviluppo:
						{else}
							{$compito_attuale.titolo_risposta}:
						{/if}
							<div class="compito_desc ui-widget-content ui-corner-all">
								{if $compito_attuale.risposta == ''}
									In attesa di valutazione....
								{else}
									{$compito_attuale.risposta}
								{/if}
							</div>
						</div>
					</div>
				{/if}
			{/foreach}

		</div>

	</div>
    <script>
	{literal}
	$(function() {
		$("#tab_compiti").tabs();
		$(".compito_desc").hide();

		$(".compito_title").click(function(){
			var id = $(this).attr('id').replace(/compito_/, '');

			$(this).parent().find('.compito_desc')
				.toggle('blind');

			$(this).css('font-weight', 'normal');

			$.ajax({
				url: "scripts/update_compiti.php?id_utente="+ $('input[name="current_user"]').eq(0).val() +"&id_notizia="+ id,
				type: "GET"
			});
		});
	});
	{/literal}
	</script>


	{if $stato_compiti == 'sviluppi_aperti'}

	<tr>
		<td valign='top' width='100%'>
			<table width='100%' align='center'>
				<tr>
					<td ALIGN='CENTER'><br><font color='#000000' size='4'>SVILUPPI APERTI ESISTENTI</font></td>
				</tr>
				<tr>
					<td ALIGN='CENTER'>
						{section name=cont1 loop=$elenco_compiti_filtrati}
		<table width='100%' align='center'>
								<tr>
									<td class='{$elenco_compiti_filtrati[cont1].emergenza_colore}' ALIGN='CENTER' colspan='3' width='100%'><b><font size='2'>Compito aperto il : {$elenco_compiti_filtrati[cont1].data_apertura_tradotta}</font></b></td>
								</tr>
								<tr>
									<td ALIGN='LEFT' width='50%'><font size='2'>Descrizione: {$elenco_compiti_filtrati[cont1].descrizione}</font></td>
									<td ALIGN='LEFT' width='50%'><font size='2'>Aperto il: {$elenco_compiti_filtrati[cont1].data_apertura_tradotta}<br>
																	   Data ultimo aggiornamento: {$elenco_compiti_filtrati[cont1].data_aggiornamento_tradotta}<br>
																	   Livello urgenza: {$elenco_compiti_filtrati[cont1].emergenza_tradotta}<br>
																	   Responsabile: {$elenco_compiti_filtrati[cont1].responsabile}<br>
																	   Status: {$elenco_compiti_filtrati[cont1].status}</font></td>
								</tr>
							</table>
						{/section}
				</tr>
			 </table>
		</td>
	</tr>

	{/if}

	{if $stato_compiti == 'sviluppi_totali'}

	<tr>
		<td valign='top' width='100%'>
			<table width='100%' align='center'>
				<tr>
					<td ALIGN='CENTER'><br><font color='#000000' size='4'>SVILUPPI TOTALI ESISTENTI</font></td>
				</tr>
				<tr>
					<td ALIGN='CENTER'>
						{section name=cont1 loop=$elenco_compiti_filtrati}
							<table width='100%' align='center'>
								<tr>
								{if $elenco_compiti_filtrati[cont1].data_chiusura == 0 }
									<td class='{$elenco_compiti_filtrati[cont1].emergenza_colore}' ALIGN='CENTER' colspan='3' width='100%'><b><font size='2'>Compito aperto il : {$elenco_compiti_filtrati[cont1].data_apertura_tradotta}</font></b></td>
								{else}
									<td class='{$elenco_compiti_filtrati[cont1].emergenza_colore}' ALIGN='CENTER' colspan='3' width='100%'><b><font size='2'>Compito aperto il : {$elenco_compiti_filtrati[cont1].data_apertura_tradotta}. Chiuso il giorno: {$elenco_compiti_filtrati[cont1].data_chiusura_tradotta}</font></b></td>
								{/if}
								</tr>
								<tr>
									<td ALIGN='LEFT' width='50%'><font size='2'>Descrizione: {$elenco_compiti_filtrati[cont1].descrizione}</font></td>
									<td ALIGN='LEFT' width='50%'><font size='2'>Aperto il: {$elenco_compiti_filtrati[cont1].data_apertura_tradotta}<br>
																	   Data ultimo aggiornamento: {$elenco_compiti_filtrati[cont1].data_aggiornamento_tradotta}<br>
																	   Data chiusura: {$elenco_compiti_filtrati[cont1].data_chiusura_tradotta}<br>
																	   Livello urgenza: {$elenco_compiti_filtrati[cont1].emergenza_tradotta}<br>
																	   Responsabile: {$elenco_compiti_filtrati[cont1].responsabile}<br>
																	   Status: {$elenco_compiti_filtrati[cont1].status}</font></td>
								</tr>
							</table>
						{/section}
				</tr>
			 </table>
		</td>
	</tr>


	{/if}



{/if}

{* }}} *}

{if $tipo_manutenzione == 'log_ins_mod_del'}

<form method='post' action='{$SCRIPT_NAME}'>
<tr>
	<td valign='top' width='100%'>
	<table width='100%' align='center'>
        <tr>
            <td ALIGN='CENTER' COLSPAN='4'><br><font color='#000000' size='4'>VERIFICA CHI/QUANDO INSERISCE/MODIFICA/CANCELLA I DATI</font></td>
        <tr>

            <td>
				<font color='#000000' size='2'>Tipo dato:</font>
				{mastercom_smart_select
					nome_pulsante="visualizzazione_abbinamenti"
					contesto="istituto"
					nome_select="tipo_dato"
					classe="classeselect_base"
					utente_corrente=$current_user
				}
					<OPTION value="voti">Voti</OPTION>
                    <OPTION value="assenze">Assenze</OPTION>
                    <OPTION value="note_disciplinari">Note disciplinari</OPTION>
                {/mastercom_smart_select}
			</td>
             <td>
				<font color='#000000' size='2'>Studente:</font>
				{mastercom_smart_select
					nome_pulsante="visualizzazione_abbinamenti"
					contesto="istituto"
					nome_select="id_studente"
					classe="classeselect_base"
					utente_corrente=$current_user
				}
					<OPTION value="NESSUNO">selezionare</OPTION>
					{section name=cont1 loop=$elenco_studenti}
                        <OPTION value="{$elenco_studenti[cont1].id_studente}">{$elenco_studenti[cont1].cognome} {$elenco_studenti[cont1].nome} - {$elenco_studenti[cont1].classe}{$elenco_studenti[cont1].sezione}</OPTION>
                    {/section}
                {/mastercom_smart_select}
			</td>
            <td><font color='#000000' size='2'> Dal: </font>{html_select_date prefix='inizio_' start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}</td>
            <td><font color='#000000' size='2'> al: </font>{html_select_date prefix='fine_' start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}</td>        </tr>
        <tr>
            <td ALIGN='CENTER' COLSPAN='4'>
				<input type='image' name='bottone' value='Stampa' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
				<input type='hidden' name='form_stato' value='{$form_stato}'>
				<input type='hidden' name='stato_principale' value='{$stato_principale}'>
				<input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
				<input type='hidden' name='tipo_manutenzione' value='log_ins_mod_del'>
				<input type='hidden' name='stato_log_ins_mod_del' value='verifica'>
				<input type='hidden' name='current_user' value='{$current_user}'>
				<input type='hidden' name='current_key' value='{$current_key}'>
			</td>
        </tr>
    </table>
</form>
	</td>
</tr>
    {if $stato_log_ins_mod_del == 'verifica'}
        <tr>
            <td valign='top' width='100%'>
                <table width='100%'>
                    <tr>
                        <td ALIGN='left' COLSPAN='9'><br><font color='#000000' size='4'>Risultati ricerca effettuata per lo studente: {$dati_studente.cognome} {$dati_studente.nome} nel periodo che va dal {$data_inizio_sel} al {$data_fine_sel}, estremi compresi.</font></td>
                    </tr>
                    {if $tipo_dato == 'voti'}
                         <tr style='background-color:#79b7e7 ;'>
                            <td ALIGN='center' colspan="3"><b><font color='#000000' size='2'>Dati voto</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='2'>Creato</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='2'>Modificato</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='2'>Eliminato</font></b></td>
                        </tr>
                         <tr style='background-color:#79b7e7;'>
                            <td ALIGN='center'><b><font color='#000000' size='2'>Voto</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='2'>Tipo</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='2'>Data</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='2'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='2'>il</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='2'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='2'>il</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='2'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='2'>il</font></b></td>
                        </tr>
                        {section name=cont1 loop=$elenco_dati_verificati}
                            {if $elenco_dati_verificati[cont1].dato_cancellato == 'SI'}
                                <tr style='background-color:#FFAB2B;'>
                            {else}
                                <tr style='background-color:#FFFF96;'>
                            {/if}
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].voto}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].tipo_tradotto}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_tradotta}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_inserisce_tradotto}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_inserimento_tradotta}</font></td>
                                {if $elenco_dati_verificati[cont1].data_inserimento != $elenco_dati_verificati[cont1].data_modifica}
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_modifica_tradotto}</font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_modifica_tradotta}</font></td>
                                {else}
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                {/if}
                                {if $elenco_dati_verificati[cont1].dato_cancellato == 'SI'}
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_modifica_tradotto}</font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].flag_canc_tradotta}</font></td>
                                {else}
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                {/if}
                            <tr>
                        {/section}
                    {elseif $tipo_dato =='assenze'}
                         <tr style='background-color:#79b7e7 ;'>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='3'>Dati assenza</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='3'>Creato</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='3'>Modificato</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='3'>Eliminato</font></b></td>
                        </tr>
                         <tr style='background-color:#79b7e7 ;'>
                            <td ALIGN='center'><b><font color='#000000' size='3'>Tipo</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>Data</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>il</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>il</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>il</font></b></td>
                        </tr>
                        {section name=cont1 loop=$elenco_dati_verificati}
                            {if $elenco_dati_verificati[cont1].dato_cancellato == 'SI'}
                                <tr style='background-color:#FFAB2B;'>
                            {else}
                                <tr style='background-color:#FFFF96;'>
                            {/if}
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].tipo_assenza_tradotta}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_tradotta}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_inserisce_tradotto}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_inserimento_tradotta}</font></td>
                                {if $elenco_dati_verificati[cont1].data_inserimento != $elenco_dati_verificati[cont1].data_modifica}
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_modifica_tradotto}</font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_modifica_tradotta}</font></td>
                                {else}
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                {/if}
                                {if $elenco_dati_verificati[cont1].dato_cancellato == 'SI'}
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_modifica_tradotto}</font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].flag_canc_tradotta}</font></td>
                                {else}
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                {/if}
                            <tr>
                        {/section}
					{elseif $tipo_dato =='note_disciplinari'}
                         <tr style='background-color:#79b7e7 ;'>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='3'>Dati nota</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='3'>Creato</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='3'>Modificato</font></b></td>
                            <td ALIGN='center' colspan="2"><b><font color='#000000' size='3'>Eliminato</font></b></td>
                        </tr>
                         <tr style='background-color:#79b7e7 ;'>
                            <td ALIGN='center'><b><font color='#000000' size='3'>Testo ufficiale</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>Data Nota</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>il</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>il</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>da Utente - Cognome Nome</font></b></td>
                            <td ALIGN='center'><b><font color='#000000' size='3'>il</font></b></td>
                        </tr>
                        {section name=cont1 loop=$elenco_dati_verificati}
                            {if $elenco_dati_verificati[cont1].dato_cancellato == 'SI'}
                                <tr style='background-color:#FFAB2B;'>
                            {else}
                                <tr style='background-color:#FFFF96;'>
                            {/if}
                                <td ALIGN='left'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].testo_ufficiale}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_tradotta}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_inserisce_tradotto}</font></td>
                                <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_inserimento_tradotta}</font></td>
                                {if $elenco_dati_verificati[cont1].data_inserimento != $elenco_dati_verificati[cont1].data_modifica}
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_modifica_tradotto}</font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].data_modifica_tradotta}</font></td>
                                {else}
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                {/if}
                                {if $elenco_dati_verificati[cont1].dato_cancellato == 'SI'}
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].chi_modifica_tradotto}</font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_verificati[cont1].flag_canc_tradotta}</font></td>
                                {else}
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                    <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                {/if}
                            <tr>
                        {/section}
                    {/if}
                </table>
            </td>
        </tr>
    {/if}
{/if}


{if $tipo_manutenzione == 'ips_ids'}

<form method='post' action='{$SCRIPT_NAME}'>
<tr>
	<td valign='top' width='100%'>
	<table width='100%' align='center'>
        <tr>
            <td ALIGN='CENTER'><br><font color='#000000' size='5'><b>IPS/IDS (Rilevamento situazioni sospette)</b></font></td>
        <tr>

            <td><font color='#000000' size='3'>Controllare il periodo dal </font>{html_select_date time=$data_partenza prefix='inizio_' start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                al </font>{html_select_date prefix='fine_' start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}</td>
        </tr>
        <tr>
            <td>
				<font color='#000000' size='3'>Studente:</font>
				{mastercom_smart_select
					nome_pulsante="visualizzazione_abbinamenti"
					contesto="istituto"
					nome_select="id_studente"
					classe="classeselect_base"
					utente_corrente=$current_user
                    valore=$id_studente
				}
					<OPTION value="TUTTI">Tutti gli studenti</OPTION>
					{section name=cont1 loop=$elenco_studenti}
                        <OPTION value="{$elenco_studenti[cont1].id_studente}">{$elenco_studenti[cont1].cognome} {$elenco_studenti[cont1].nome} - {$elenco_studenti[cont1].classe}{$elenco_studenti[cont1].sezione}</OPTION>
                    {/section}
                {/mastercom_smart_select}
			</td>
        </tr>
        <tr>
            <td>
				<font color='#000000' size='3'>Classe:</font>
				{mastercom_smart_select
					nome_pulsante="visualizzazione_abbinamenti"
					contesto="istituto"
					nome_select="id_classe"
					classe="classeselect_base"
					utente_corrente=$current_user
                    valore=$id_classe
				}
					<OPTION value="TUTTI">Tutte le classi</OPTION>
					{section name=cont1 loop=$elenco_classi}
                        <OPTION value="{$elenco_classi[cont1].id_classe}">{$elenco_classi[cont1].classe} {$elenco_classi[cont1].sezione} - {$elenco_classi[cont1].descrizione_indirizzi}</OPTION>
                    {/section}
                {/mastercom_smart_select}
			</td>
        </tr>
        <tr>
            <td>
				<font color='#000000' size='3'>Utente:</font>
				{mastercom_smart_select
					nome_pulsante="visualizzazione_abbinamenti"
					contesto="istituto"
					nome_select="id_utente"
					classe="classeselect_base"
					utente_corrente=$current_user
				}
					<OPTION value="TUTTI_PROF">Tutti gli utenti</OPTION>
					{section name=cont1 loop=$elenco_utenti}
                    {if $elenco_utenti[cont1].tipo_utente == 'P'}
                        <OPTION value="{$elenco_utenti[cont1].id_utente}">{$elenco_utenti[cont1].cognome} {$elenco_utenti[cont1].nome} (Professore)</OPTION>
                    {else}
                        <OPTION value="{$elenco_utenti[cont1].id_utente}">{$elenco_utenti[cont1].cognome} {$elenco_utenti[cont1].nome} (Amministratore)</OPTION>
                    {/if}
                    {/section}
                {/mastercom_smart_select}
			</td>
        </tr>
        <tr>
            <td>
                <font color='#000000' size='3'>Seleziona il tipo di dato da ricercare</font>
                {mastercom_smart_select
					nome_pulsante="visualizzazione_abbinamenti"
					contesto="istituto"
					nome_select="dati_visualizzati"
					classe="classeselect_base"
					utente_corrente=$current_user
                    valore="TUTTI"
				}
					<OPTION value="TUTTI">Tutti i dati</OPTION>
					<OPTION value="cancellati">Solo i dati cancellati</OPTION>
					<OPTION value="modificati">Solo i dati modificati</OPTION>
					<OPTION value="modificati e cancellati">Sia dati modificati che dati cancellati</OPTION>
                {/mastercom_smart_select}
                <br>
                <br>
			</td>
        </tr>
        <tr>
            <td>
                <font color='#000000' size='3'><b>Selezionare le possibili situazioni sospette che si desidera analizzare:</b></font>
                <br>
                <input type='checkbox' name='orario_sospetto' value='attivato'>
                <font color='#000000' size='3'>Orario in cui avviene la modifica, sospetto tra le ore</font> {html_select_time prefix='ora_inizio_' time='00:00' display_seconds=false display_minutes=false} e le {html_select_time prefix='ora_fine_' time='06:00' display_seconds=false display_minutes=false}
			</td>
        </tr>
        <tr>
            <td>
                <input type='checkbox' name='voti_sospetti' value='attivato'>
                <font color='#000000' size='3'>Estrarre i voti sospetti (tutti i voti migliorati)</font>
			</td>
        </tr>
        <tr>
            <td ALIGN='CENTER'>
				<input type='image' name='bottone' value='Stampa' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='conferma'>
				<input type='hidden' name='form_stato' value='{$form_stato}'>
				<input type='hidden' name='stato_principale' value='{$stato_principale}'>
				<input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
				<input type='hidden' name='tipo_manutenzione' value='ips_ids'>
				<input type='hidden' name='stato_ips_ids' value='verifica'>
				<input type='hidden' name='current_user' value='{$current_user}'>
				<input type='hidden' name='current_key' value='{$current_key}'>
			</td>
        </tr>
    </table>
</form>
	</td>
</tr>
    {if $stato_ips_ids == 'verifica'}
        <tr>
            <td valign='top' width='100%'>
                <font color='#000000' size='4'>Risultati ricerca effettuata con i seguenti criteri:<br>
                - periodo che va dal {$data_inizio_sel} al {$data_fine_sel}, estremi compresi.<br>
                {if $orario_sospetto == 'attivato'}
                    - tra le ore {$ora_inizio_Hour} e {$ora_fine_Hour}, estremi compresi.<br>
                {/if}
                {if $id_studente > 0 }
                    - per lo studente {$dati_studente.cognome} {$dati_studente.nome}.<br>
                {/if}
                {if $id_classe > 0 }
                    - per la classe {$dati_classe.classe}{$dati_classe.sezione}.<br>
                {/if}
                {if $id_utente > 0 }
                    {if $dati_utente.tipo_utente == 'P'}
                    - modificati/inseriti dall'utente {$dati_utente.cognome} {$dati_utente.nome} (Professore).<br>
                    {else}
                    - modificati/inseriti dall'utente {$dati_utente.cognome} {$dati_utente.nome} (Amministratore).<br>
                    {/if}
                {/if}
                {if $dati_visualizzati != "TUTTI"}
                    - visualizzare tutti i dati {$dati_visualizzati}.<br>
                {/if}
                </font>
            </td>
        </tr>
        <tr>
            <td ALIGN='center'><font color='#000000' size='4'>Elenco voti sospetti nel periodo controllato</font></td>
        </tr>
        <tr>
            <td valign='top' width='100%'>
                <table width='100%' border="1px solid">
                    <tr style='background-color:#79b7e7 ;'>
                       <td ALIGN='center' colspan="3"><b><font color='#000000' size='2'>Dati studente</font></b></td>
                       <td ALIGN='center' colspan="4"><b><font color='#000000' size='2'>Dati voto</font></b></td>
                       <td ALIGN='center' colspan="2"><b><font color='#000000' size='2'>Creato</font></b></td>
                       <td ALIGN='center' colspan="2"><b><font color='#000000' size='2'>Modificato</font></b></td>
                       <td ALIGN='center' colspan="2"><b><font color='#000000' size='2'>Eliminato</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>Info</font></b></td>
                   </tr>
                    <tr style='background-color:#79b7e7;'>
                       <td ALIGN='center'><b><font color='#000000' size='2'>Cognome</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>Nome</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>Classe</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>Materia</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>Voto</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>Tipo</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>Data</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>da Utente - Cognome Nome</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>il</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>da Utente - Cognome Nome</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>il</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>da Utente - Cognome Nome</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'>il</font></b></td>
                       <td ALIGN='center'><b><font color='#000000' size='2'></font></b></td>
                   </tr>
                   {section name=cont1 loop=$elenco_dati_voti}
                       <form method='post' action='{$SCRIPT_NAME}'>
                       {if $elenco_dati_voti[cont1].dato_cancellato == 'SI'}
                           <tr style='background-color:#FFAB2B;'>
                       {else}
                           <tr style='background-color:#FFFF96;'>
                       {/if}
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].cognome}</font></td>
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].nome}</font></td>
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].descrizione_classe}</font></td>
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].descrizione}</font></td>
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].voto}</font></td>
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].tipo_tradotto}</font></td>
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].data_tradotta}</font></td>
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].chi_inserisce_tradotto}</font></td>
                           <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].data_inserimento_tradotta}</font></td>
                           {if $elenco_dati_voti[cont1].data_inserimento != $elenco_dati_voti[cont1].data_modifica}
                               <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].chi_modifica_tradotto}</font></td>
                               <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].data_modifica_tradotta}</font></td>
                           {else}
                               <td ALIGN='center'><font color='#000000' size='2'></font></td>
                               <td ALIGN='center'><font color='#000000' size='2'></font></td>
                           {/if}
                           {if $elenco_dati_voti[cont1].dato_cancellato == 'SI'}
                               <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].chi_modifica_tradotto}</font></td>
                               <td ALIGN='center'><font color='#000000' size='2'>{$elenco_dati_voti[cont1].flag_canc_tradotta}</font></td>
                           {else}
                               <td ALIGN='center'><font color='#000000' size='2'></font></td>
                               <td ALIGN='center'><font color='#000000' size='2'></font></td>
                           {/if}
                           <td ALIGN='center'><font color='#000000' size='2'>
                                <input type='image' name='bottone' value='Info' src='icone/icona.php?icon=ok&label_bg=44621c&size={$dimensione_immagini}' alt='info'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                <input type='hidden' name='stato_secondario' value='tipo_manutenzione'>
                                <input type='hidden' name='tipo_manutenzione' value='ips_ids'>
                                <input type='hidden' name='stato_ips_ids' value='verifica'>
                                <input type='hidden' name='stato_ips_ids_dettaglio' value='info'>
                                <input type='hidden' name='id_voto' value='{$elenco_dati_voti[cont1].id_voto}'>
                                <input type='hidden' name='dati_visualizzati' value='{$dati_visualizzati}'>
                                <input type='hidden' name='voti_sospetti' value='{$voti_sospetti}'>
                                <input type='hidden' name='orario_sospetto' value='{$orario_sospetto}'>
                                <input type='hidden' name='ora_inizio_Hour' value='{$ora_inizio_Hour}'>
                                <input type='hidden' name='ora_fine_Hour' value='{$ora_fine_Hour}'>
                                <input type='hidden' name='id_studente' value='{$id_studente}'>
                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                <input type='hidden' name='id_utente' value='{$id_utente}'>
                                <input type='hidden' name='data_inizio' value='{$data_inizio}'>
                                <input type='hidden' name='data_fine' value='{$data_fine}'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                               </font>
                           </td>
                           {if $elenco_dati_voti[cont1].info != ''}
                           <tr>
                                <td ALIGN='center'><font color='#000000' size='2'></font></td>
                                <td ALIGN='center' colspan="13">
                                    <table width='100%' border="1px solid">
                                        {section name=cont2 loop=$elenco_dati_voti[cont1].info}
                                            {if $elenco_dati_voti[cont1].info[cont2].colore_colonna == 1}
                                                <tr style='background-color:#8efcb2'>
                                            {else}
                                                <tr style='background-color:#B4FFCD;'>
                                            {/if}
                                           <td ALIGN='center' rowspan="2"><b><font color='#000000' size='2'>Data evento<br>{$elenco_dati_voti[cont1].info[cont2].dato.data_modifica_tradotta}</font></b></td>
                                           <td ALIGN='center'><b><font color='#000000' size='2'>Voto<br></font></b><font color='#000000' size='2'>{$elenco_dati_voti[cont1].info[cont2].dato.voto}</font></td>
                                           <td ALIGN='center'><b><font color='#000000' size='2'>Tipo voto<br></font></b><font color='#000000' size='2'>{$elenco_dati_voti[cont1].info[cont2].dato.tipo_tradotto}</font></td>
                                           <td ALIGN='center'><b><font color='#000000' size='2'>Data<br></font></b><font color='#000000' size='2'>{$elenco_dati_voti[cont1].info[cont2].dato.data_tradotta}</font></td>
                                           <td ALIGN='center'><b><font color='#000000' size='2'>Note<br></font></b><font color='#000000' size='2'>{$elenco_dati_voti[cont1].info[cont2].dato.note}</font></td>
                                           <td ALIGN='center'><b><font color='#000000' size='2'>Chi modifica<br></font></b><font color='#000000' size='2'>{$elenco_dati_voti[cont1].info[cont2].dato.dati_utente.cognome} {$elenco_dati_voti[cont1].info[cont2].dato.dati_utente.nome}</font></td>
                                           <td ALIGN='center'><b><font color='#000000' size='2'>Materia<br></font></b><font color='#000000' size='2'>{$elenco_dati_voti[cont1].info[cont2].dato.dati_materia.descrizione}</font></td>
                                        </tr>
                                            {if $elenco_dati_voti[cont1].info[cont2].colore_colonna == 1}
                                                <tr style='background-color:#8efcb2;'>
                                            {else}
                                                <tr style='background-color:#B4FFCD;'>
                                            {/if}
                                           <td ALIGN='center'><b><font color='#000000' size='2'>IP: </b>{$elenco_dati_voti[cont1].info[cont2].dettaglio_utente.IP_UTENTE}</font></td>
                                           <td ALIGN='center'><b><font color='#000000' size='2'>Data login: </b>{$elenco_dati_voti[cont1].info[cont2].dettaglio_utente.data_login_tradotta}</font></td>
                                           <td ALIGN='center' colspan="4"><b><font color='#000000' size='2'>Sistema utilizzato: </b>{$elenco_dati_voti[cont1].info[cont2].dettaglio_utente.HTTP_USER_AGENT}</font></td>
                                        </tr>
                                        {/section}
                                    </table>
                                </td>
                           </tr>
                            {/if}
                       </tr>
                    </form>
                   {/section}
                </table>
            </td>
        </tr>
    {/if}
{/if}
</table>

{include file="footer_amministratore.tpl"}
